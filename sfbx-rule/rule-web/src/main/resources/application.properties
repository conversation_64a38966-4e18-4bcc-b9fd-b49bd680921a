debug=false
server.port=8090

spring.main.allow-circular-references=true

#spring.datasource.type=org.apache.tomcat.jdbc.pool.DataSource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.url=******************************************************************************************************************************************************************************
spring.datasource.url=**************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=pass
spring.datasource.druid.initial-size=5
spring.datasource.druid.max-active=10
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-wait=60000
spring.datasource.druid.validation-query=SELECT 1 
spring.datasource.druid.validation-query-timeout=6000
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=100000


urule.repository.databasetype=mysql
urule.repository.datasourcename=dataSource
