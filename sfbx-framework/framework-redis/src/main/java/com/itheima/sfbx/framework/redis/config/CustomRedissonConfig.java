package com.itheima.sfbx.framework.redis.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnClass(RedissonClient.class)
public class CustomRedissonConfig {
    private final RedisProperties redisProperties;

    @Bean(
            destroyMethod = "shutdown"
    )
    @Primary
    @ConditionalOnProperty(name = "spring.redis.host", matchIfMissing = false)
    public RedissonClient redisson() {
        try {
            Config config = new Config();
            String prefix = "redis://";
            SingleServerConfig singleServerConfig = config.useSingleServer()
                    .setAddress(prefix + this.redisProperties.getHost() + ":" + this.redisProperties.getPort())
                    .setDatabase(this.redisProperties.getDatabase())
                    .setConnectionMinimumIdleSize(1)
                    .setConnectionPoolSize(5)
                    .setConnectTimeout(3000)
                    .setTimeout(3000);

            if (this.redisProperties.getPassword() != null && !this.redisProperties.getPassword().isEmpty()) {
                singleServerConfig.setPassword(this.redisProperties.getPassword());
            }

            config.setCodec(JsonJacksonCodec.INSTANCE);

            RedissonClient client = Redisson.create(config);
            log.info("Redis连接成功: {}:{}", this.redisProperties.getHost(), this.redisProperties.getPort());
            return client;
        } catch (Exception e) {
            log.warn("Redis连接失败，将使用空实现: {}", e.getMessage());
            throw e; // 让Spring处理这个异常
        }
    }
}
