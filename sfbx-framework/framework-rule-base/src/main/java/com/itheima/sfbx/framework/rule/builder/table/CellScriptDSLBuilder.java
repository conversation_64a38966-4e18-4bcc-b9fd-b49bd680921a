/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.builder.table;

import com.itheima.sfbx.framework.rule.RuleException;
import com.itheima.sfbx.framework.rule.dsl.CellScriptRuleParserBaseVisitor;
import com.itheima.sfbx.framework.rule.dsl.RuleParserLexer;
import com.itheima.sfbx.framework.rule.dsl.RuleParserParser;
import com.itheima.sfbx.framework.rule.dsl.ScriptDecisionTableErrorListener;
import org.antlr.v4.runtime.ANTLRInputStream;
import org.antlr.v4.runtime.CommonTokenStream;

/**
 * <AUTHOR>
 * @since 2015年5月6日
 */
public class CellScriptDSLBuilder {
	public String buildCriteriaScript(String script,String propertyName){
		ANTLRInputStream antlrInputStream=new ANTLRInputStream(script);
		RuleParserLexer lexer=new RuleParserLexer(antlrInputStream);
		CommonTokenStream tokenStream=new CommonTokenStream(lexer);
		RuleParserParser parser=new RuleParserParser(tokenStream);
		ScriptDecisionTableErrorListener errorListener=new ScriptDecisionTableErrorListener();
		parser.addErrorListener(errorListener);
		CellScriptRuleParserBaseVisitor visitor=new CellScriptRuleParserBaseVisitor(propertyName);
		String resultScript=visitor.visit(parser.decisionTableCellCondition());
		String error=errorListener.getErrorMessage();
		if(error!=null){
			throw new RuleException("Script Parse error:"+error);
		}
		return resultScript;
	}
}
