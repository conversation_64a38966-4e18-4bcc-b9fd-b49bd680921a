# 专门用于测试 Timer 修复的配置文件
spring:
  # 完全禁用 Nacos
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
        
  # 禁用所有可能导致问题的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.actuate.redis.RedisReactiveHealthContributorAutoConfiguration
      - org.springframework.boot.autoconfigure.actuate.redis.RedisHealthContributorAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.jdbc.DataSourcePoolMetricsAutoConfiguration

  # 禁用缓存
  cache:
    type: none
    
  # 禁用健康检查
  management:
    health:
      redis:
        enabled: false
      db:
        enabled: false
    endpoints:
      enabled-by-default: false
      
  # 使用内存数据库进行测试（避免数据库配置问题）
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  # H2 数据库控制台（可选）
  h2:
    console:
      enabled: true
      
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# 日志配置
logging:
  level:
    com.itheima.sfbx: DEBUG
    org.springframework.cache: OFF
    org.redisson: OFF
    org.springframework.boot.actuate: WARN
    org.springframework.web: WARN
    
# 服务器配置
server:
  port: 7071
