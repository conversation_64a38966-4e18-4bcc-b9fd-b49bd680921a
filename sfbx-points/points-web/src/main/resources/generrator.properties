#\u6570\u636E\u5E93\u5730\u5740
url=************************************************************************************************************************
#\u6570\u636E\u5E93\u8D26\u53F7
userName=root
#\u6570\u636E\u5E93\u5BC6\u7801
password=pass
#services\u7684src\u8DEF\u5F84
serviceProjectPath=D:/sfbx-cloud/sfbx-points/points-web-2
#VO\u7684src\u8DEF\u5F84
dtoOProjectPath=D:/sfbx-cloud/sfbx-points/points-web-2
#\u4F5C\u8005
author=Admin
#\u5305\u540D\u79F0
parent=com.itheima.sfbx
#\u6A21\u5757\u540D\u79F0
moduleName=points
#\u6570\u636E\u5E93\u8868\u524D\u7F00
tablePrefix =tab_
#\u9700\u8981\u751F\u6210\u7684\u8868\u540D\uFF0C\u4F7F\u7528\uFF0C\u5206\u5272
tableName=tab_do_insure_category
#\u9700\u8981\u751F\u6210\u9644\u4EF6\u76F8\u5173\u7684\u8868
#tablesFile =tab_category,tab_combination,tab_customer_card,tab_customer_info,tab_customer_relation,tab_insurance
tablesFile =
#pojo\u7684\u7236\u7C7B
SuperEntityClass = com.itheima.sfbx.framework.mybatisplus.basic.BasePojo
#pojo\u7684\u901A\u7528\u5B57\u6BB5
superEntityColumns = id,create_time,update_time,create_by,update_by,data_state
#\u751F\u6210\u89C4\u5219
constant=true
constant.ftl.path=/templates/constant.java
enums=true
enums.ftl.path=/templates/enums.java
dto=true
dto.ftl.path=/templates/dto.java
entity=true
entity.ftl.path=/templates/entity.java
mapper=true
mapper.ftl.path=/templates/mapper.java
service=true
service.ftl.path=/templates/service.java
serviceImpl=true
serviceImpl.ftl.path=/templates/serviceImpl.java
controller=true
controller.ftl.path=/templates/controller.java
