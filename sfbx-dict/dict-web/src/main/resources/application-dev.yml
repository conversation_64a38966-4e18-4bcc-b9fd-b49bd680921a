# 本地开发环境配置
spring:
  # Redis 配置
  redis:
    host: ***************
    port: 6379
    database: 0
    password: # 如果Redis没有密码，留空
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# 数据库配置（如果需要的话）
# spring:
#   datasource:
#     url: *********************************************************************************************************
#     username: root
#     password: your_password
#     driver-class-name: com.mysql.cj.jdbc.Driver

# 日志配置
logging:
  level:
    com.itheima.sfbx: DEBUG
    org.redisson: INFO
