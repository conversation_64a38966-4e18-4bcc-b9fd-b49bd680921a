<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Repository PUBLIC "-//The Apache Software Foundation//DTD Jackrabbit 1.5//EN" "http://jackrabbit.apache.org/dtd/repository-1.5.dtd">
<Repository>
  <FileSystem class="com.itheima.sfbx.rule.console.repository.database.system.DerbyFileSystem">
      <param name="schemaObjectPrefix" value="repo_"/>
  </FileSystem>
  <Security appName="Jackrabbit">
    <AccessManager class="org.apache.jackrabbit.core.security.simple.SimpleAccessManager"></AccessManager>
    <LoginModule class="org.apache.jackrabbit.core.security.simple.SimpleLoginModule">
      <param name="anonymousId" value="anonymous" />
      <param name="adminId" value="admin" />
    </LoginModule>
  </Security>
  <DataStore class="com.itheima.sfbx.rule.console.repository.database.store.DerbyDataStore">
      <param name="schemaObjectPrefix" value="repo_ds_"/>
  </DataStore>
  <Workspaces rootPath="${rep.home}/workspaces" defaultWorkspace="default" />
  <Workspace name="default">
    <FileSystem class="com.itheima.sfbx.rule.console.repository.database.system.DerbyFileSystem">
      <param name="schemaObjectPrefix" value="repo_${wsp.name}_"/>
    </FileSystem>
    <PersistenceManager class="com.itheima.sfbx.rule.console.repository.database.manager.DerbyPersistenceManager">
      <param name="schemaObjectPrefix" value="repo_pm_${wsp.name}_"/>
    </PersistenceManager>
  </Workspace>
  <Versioning rootPath="${rep.home}/version">
    <FileSystem class="com.itheima.sfbx.rule.console.repository.database.system.DerbyFileSystem">
      <param name="schemaObjectPrefix" value="repo_fsver_"/>
    </FileSystem>
    <PersistenceManager class="com.itheima.sfbx.rule.console.repository.database.manager.DerbyPersistenceManager">
      <param name="schemaObjectPrefix" value="repo_ver_"/>
    </PersistenceManager>
  </Versioning>
  
	<Cluster syncDelay="5000">
	    <Journal class="com.itheima.sfbx.rule.console.repository.database.journal.DatabaseJournal">
	      <param name="schemaObjectPrefix" value="journal_"/>
	    </Journal>
	</Cluster>
</Repository>
