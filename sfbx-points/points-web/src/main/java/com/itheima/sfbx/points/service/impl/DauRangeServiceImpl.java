package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DauRangeMapper;
import com.itheima.sfbx.points.pojo.DauRange;
import com.itheima.sfbx.points.service.IDauRangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：用户日活跃数范围服务实现类
 */
@Slf4j
@Service
public class DauRangeServiceImpl extends ServiceImpl<DauRangeMapper, DauRange> implements IDauRangeService {


}
