package com.itheima.sfbx.insurance.constant;

import com.itheima.sfbx.framework.commons.constant.basic.CacheConstant;
import com.itheima.sfbx.framework.commons.constant.basic.SuperConstant;

/**
* @Description：客户关系表缓存常量
*/
public class CustomerRelationConstant extends SuperConstant {

    //本人
    public static final String SELF= "0";

    //子女
    public static final String CHILDREN= "3";

    //配偶
    public static final String SPOUSE = "1";

    //父母
    public static final String PARENTS= "2";

    //其他
    public static final String OTHER= "4";

}
