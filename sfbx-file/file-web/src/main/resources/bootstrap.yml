#服务配置
server:
  #端口
  port: 7075
  #服务编码
  tomcat:
    uri-encoding: UTF-8
spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB
  profiles:
    active: dev
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  #应用配置
  application:
    #应用名称
    name: file-web
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848 # nacos注册中心
        group: SEATA_GROUP
        service: ${spring.application.name}
      config:
        server-addr: **************:8848 # nacos配置中心地址
        group: SEATA_GROUP
        file-extension: yml
        shared-configs: # 共享配置
          - data-id: shared-spring-seata.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-redisson.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-mybatis-plus.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-stream-rabbit-basic.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-stream-rabbit-source-file.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
logging:
  config: classpath:logback.xml
