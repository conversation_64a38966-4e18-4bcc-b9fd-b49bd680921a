# 本地开发配置 - 完全禁用 Redis 和缓存
spring:
  # 完全禁用 Nacos 配置中心
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
        
  # 禁用所有 Redis 和缓存相关的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.actuate.redis.RedisReactiveHealthContributorAutoConfiguration
      - org.springframework.boot.autoconfigure.actuate.redis.RedisHealthContributorAutoConfiguration

  # 禁用缓存
  cache:
    type: none
    
  # 禁用健康检查中的 Redis
  management:
    health:
      redis:
        enabled: false
    endpoint:
      health:
        show-details: always

# 数据库配置（请根据实际情况修改）
spring:
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: root  # 请修改为实际密码
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # MyBatis Plus 配置
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.itheima.sfbx: INFO
    org.springframework.cache: OFF
    org.redisson: OFF
    org.springframework.boot.actuate: WARN
    
# 服务器配置
server:
  port: 7071
