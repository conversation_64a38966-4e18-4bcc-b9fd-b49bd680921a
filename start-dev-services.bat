@echo off
echo 检查开发环境服务状态...

echo.
echo 1. 检查Redis服务...
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo Redis服务正在运行
) else (
    echo Redis服务未运行，请启动Redis服务
    echo 可以使用以下命令启动Redis:
    echo   - 如果使用Docker: docker run -d -p 6379:6379 redis:latest
    echo   - 如果本地安装: redis-server
    echo.
    pause
    exit /b 1
)

echo.
echo 2. 检查MySQL服务...
mysql -u root -p -e "SELECT 1;" >nul 2>&1
if %errorlevel% equ 0 (
    echo MySQL服务正在运行
) else (
    echo MySQL服务未运行或连接失败
    echo 请确保MySQL服务已启动并且连接信息正确
)

echo.
echo 3. 检查Nacos服务...
curl -s http://localhost:8848/nacos >nul 2>&1
if %errorlevel% equ 0 (
    echo Nacos服务正在运行
) else (
    echo Nacos服务未运行
    echo 注意: 如果不使用Nacos，请确保本地配置文件已正确设置
)

echo.
echo 所有服务检查完成！
echo 现在可以启动应用程序了。
pause
