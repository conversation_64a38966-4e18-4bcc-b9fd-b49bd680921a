<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>sfbx-framework</artifactId>
    <groupId>com.itheima.sfbx</groupId>
    <version>2.0-SNAPSHOT</version>
  </parent>
  <!--基础模块-mybatis-plus支持-->
  <artifactId>framework-rabbitmq</artifactId>
  <name>framework-rabbitmq</name>
  <!-- FIXME change it to the project's website -->
  <url>http://www.example.com</url>

  <dependencies>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
    </dependency>
  </dependencies>
</project>
