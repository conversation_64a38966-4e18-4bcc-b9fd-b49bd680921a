<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.itheima.sfbx</groupId>
    <artifactId>sfbx-file</artifactId>
    <version>2.0-SNAPSHOT</version>
  </parent>
  <!--文件处理web模块-->
  <artifactId>file-web</artifactId>
  <name>file-web</name>
  <!-- FIXME change it to the project's website -->
  <url>http://www.example.com</url>

  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alicloud-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-rabbitmq</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-seata</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-mybatis-plus</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-knife4j-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.qiniu</groupId>
      <artifactId>qiniu-java-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.3.1</version>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.yml</include>
          <include>**/*.properties</include>
          <include>**/*.xml</include>
          <include>**/*.yaml</include>
          <include>**/*.txt</include>
        </includes>
      </resource>
    </resources>
    <finalName>file-web</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
