# 禁用Redis的配置文件
spring:
  # 禁用Redis自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration

  # 禁用缓存
  cache:
    type: none

# 数据库配置（如果需要的话）
# spring:
#   datasource:
#     url: *********************************************************************************************************
#     username: root
#     password: your_password
#     driver-class-name: com.mysql.cj.jdbc.Driver

# 日志配置
logging:
  level:
    com.itheima.sfbx: DEBUG
    root: INFO
