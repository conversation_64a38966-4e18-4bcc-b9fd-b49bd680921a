/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.action;


/**
 * <AUTHOR>
 * @since 2015年4月8日
 */
public abstract class AbstractAction implements Action {
	private int priority;
	protected boolean debug;
	@Override
	public int compareTo(Action o) {
		return o.getPriority()-priority;
	}
	@Override
	public int getPriority() {
		return priority;
	}
	
	@Override
	public void setDebug(boolean debug) {
		this.debug=debug;
	}
	public void setPriority(int priority) {
		this.priority = priority;
	}
}
