package com.itheima.sfbx.insurance.enums;

import com.itheima.sfbx.framework.commons.enums.basic.IBaseEnum;

/**
* @ClassName WarrantyEnum.java
* @Description 保险合同枚举
*/

public enum WarrantyEnum implements IBaseEnum {

    PAGE_FAIL(53001, "查询保险合同分页失败"),
    LIST_FAIL(53002, "查询保险合同列表失败"),
    FIND_ONE_FAIL(53003, "查询保险合同对象失败"),
    SAVE_FAIL(53004, "保存保险合同失败"),
    UPDATE_FAIL(53005, "修改保险合同失败"),
    DEL_FAIL(53006, "删除保险合同失败")
    ;

    private Integer code;

    private String msg;

    WarrantyEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
