#\u6570\u636E\u5E93\u5730\u5740
url=********************************************************************************************************************************
#\u6570\u636E\u5E93\u8D26\u53F7
userName=root
#\u6570\u636E\u5E93\u5BC6\u7801
password=pass
#services\u7684src\u8DEF\u5F84
serviceProjectPath=E:/itheima-project-parent/itheima-project-cloud/itheima-module-file/module-file-web
#VO\u7684src\u8DEF\u5F84
voProjectPath=E:/itheima-project-parent/itheima-project-cloud/itheima-component/component-vo
#\u4F5C\u8005
author=Admin
#\u5305\u540D\u79F0
parent=com.itheima
#\u6A21\u5757\u540D\u79F0
moduleName=project
#\u6570\u636E\u5E93\u8868\u524D\u7F00
tablePrefix =tab_
#\u9700\u8981\u751F\u6210\u7684\u8868\u540D\uFF0C\u4F7F\u7528\uFF0C\u5206\u5272
tableName= tab_file_part
#\u9700\u8981\u751F\u6210\u9644\u4EF6\u76F8\u5173\u7684\u8868
tablesFile=
#pojo\u7684\u7236\u7C7B
SuperEntityClass = com.itheima.easy.basic.BasicPojo
#pojo\u7684\u901A\u7528\u5B57\u6BB5
superEntityColumns = id,created_time,updated_time,sharding_id,enable_flag
#\u751F\u6210\u89C4\u5219
constant=true
constant.ftl.path=/templates/constant.java
enums=true
enums.ftl.path=/templates/enums.java
vo=true
vo.ftl.path=/templates/vo.java
entity=true
entity.ftl.path=/templates/entity.java
mapper=true
mapper.ftl.path=/templates/mapper.java
service=true
service.ftl.path=/templates/service.java
serviceImpl=true
serviceImpl.ftl.path=/templates/serviceImpl.java
face=false
face.ftl.path=/templates/face.java
faceImpl=false
faceImpl.ftl.path=/templates/faceImpl.java
controller=false
controller.ftl.path=/templates/controller.java
