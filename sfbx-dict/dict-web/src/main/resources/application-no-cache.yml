# 禁用缓存的配置文件 - 用于测试 Timer 修复
spring:
  # 禁用 Redis 和缓存相关的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - com.itheima.sfbx.framework.redis.config.CustomRedissonConfig
#      - com.itheima.sfbx.framework.redis.config.RedisCacheConfig

  # 禁用缓存
  cache:
    type: none

# 数据库配置（如果需要的话）
# spring:
#   datasource:
#     url: *********************************************************************************************************
#     username: root
#     password: your_password
#     driver-class-name: com.mysql.cj.jdbc.Driver

# 日志配置
logging:
  level:
    com.itheima.sfbx: DEBUG
    org.redisson: INFO
    org.springframework.cache: DEBUG
