package com.itheima.sfbx;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.itheima.sfbx.auth.BoleeEncryptor;
import com.itheima.sfbx.constants.BoleeSecurityConstant;
import com.itheima.sfbx.dto.CredentialsDTO;
import com.itheima.sfbx.dto.EncodeDTO;
import com.itheima.sfbx.utils.SecurityUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求模板类
 *
 * <p>提供统一的HTTP请求发送模板，集成了加密、解密、签名验证等安全功能。
 * 支持RSA+AES混合加密方式，确保数据传输安全。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>统一的HTTP请求发送接口</li>
 *   <li>自动加密请求数据（RSA+AES混合加密）</li>
 *   <li>自动解密响应数据</li>
 *   <li>数字签名验证</li>
 *   <li>可配置的请求超时设置</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>{@code
 * RequestTemplate template = RequestTemplate.builder()
 *     .privateKey("私钥")
 *     .publicKey("公钥")
 *     .appId("应用ID")
 *     .uriBuilder(new URIBuilder("http://example.com/api"))
 *     .build();
 *
 * ResponseDTO response = template.doRequest(requestData, ResponseDTO.class);
 * }</pre>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/12/28
 */
@Data
public class RequestTemplate {

    /**
     * RSA私钥，用于加密AES密钥和数字签名
     */
    private String privateKey;

    /**
     * RSA公钥，用于解密AES密钥和验证签名
     */
    private String publicKey;

    /**
     * 应用标识ID，用于身份认证
     */
    private String appId;

    /**
     * URI构建器，用于构建请求URL
     */
    private URIBuilder uriBuilder;

    /**
     * HTTP请求配置，包含超时设置等参数
     */
    private RequestConfig requestConfig;

    @Builder
    public RequestTemplate(String privateKey, String publicKey, String appId, URIBuilder uriBuilder,RequestConfig requestConfig) {
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.appId = appId;
        this.uriBuilder = uriBuilder;
        this.requestConfig = requestConfig;
    }


    public <T> T doRequest(Object params,Class<T> t) throws URISyntaxException, IOException {
        CloseableHttpClient httpclient = BoleeHttpClientBuilder.create()
                .withCredentials(appId, privateKey)
                .withValidator(publicKey)
                .build();
        //=============基础配置===================
        RequestConfig requestConfig = null;
        if(ObjectUtil.isNull(requestConfig)) {
            requestConfig = RequestConfig.custom()
                    .setSocketTimeout(100000)
                    .setConnectTimeout(100000)
                    .build();
        }else{
            requestConfig = this.requestConfig;
        }
        try {
            URI url = uriBuilder.build();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            EncodeDTO encodeDTO = new EncodeDTO();
            JSONObject body = JSONUtil.parseObj(params);
            encodeDTO.setBody(body.toString());
            if(new BoleeEncryptor(privateKey).encode(encodeDTO)){
                StringEntity stringEntity = new StringEntity(encodeDTO.getEncodeBody(), "utf-8");
                httpPost.setEntity(stringEntity);
                httpPost.addHeader(BoleeSecurityConstant.HEAD_NAME_BODY_KEY,encodeDTO.getHeadAESSecurityKey());
                httpPost.addHeader("Content-Type","application/json;charset=utf-8");
                CloseableHttpResponse apiRes = httpclient.execute(httpPost);
                //验签完毕后需要对数据进行解密
                HttpEntity entity = apiRes.getEntity();
                String content = EntityUtils.toString(entity);
                String responseJson = decryptRequestBody(content, apiRes.getFirstHeader(BoleeSecurityConstant.HEAD_NAME_BODY_KEY).getValue());
                if(StrUtil.isEmpty(responseJson)){
                    throw new RuntimeException("参数解析为空");
                }
                return JSONUtil.toBean(responseJson, t);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return null;
    }

    /**
     * 解密请求body体
     *
     * @param dody 返回数据对象
     * @param aesKeyRSA 获取请求头中加密后的AES密钥
     * @return
     */
    private String decryptRequestBody(String dody, String aesKeyRSA) {
        try {
            //先使用rsa解密aes秘钥
            RSA rsa = SecureUtil.rsa(null, publicKey);
            String aesKey = SecurityUtil.decryptFromStringRSAPublicKey(rsa, aesKeyRSA);
            //使用aes进行解密
            // 使用解密后的AES密钥进行AES解密请求体数据
            String requestBody = SecurityUtil.decryptFromStringAES(dody, Mode.CBC, Padding.ZeroPadding,aesKey);
            return requestBody;
        } catch (Exception e) {
            e.printStackTrace();
            // 解密失败，可以根据你的需求进行异常处理
            return null;
        }
    }

    /**
     * 构建请求参数
     * 1694953272
     * xHct36JgHbj6tXBx
     * Modified: {"msg":"ok","code":"100","data":null}
     *
     * @param authorization
     * @param response
     * @return
     */
    private CredentialsDTO buildRequestParam(String authorization, CloseableHttpResponse response) {
        try {
            authorization = authorization.replaceAll("\"", "");
            String[] authorizations = authorization.split(",");
            Map<String, String> authMap = new HashMap<>();
            for (String authorizationIndex : authorizations) {
                String[] split = authorizationIndex.split("=");
                authMap.put(split[0], split[1].substring(0, split[1].length()));
            }
            CredentialsDTO credentialsDTO = new CredentialsDTO();
            credentialsDTO.setNonce(authMap.get("nonce_str"));
            credentialsDTO.setTimestamp(authMap.get("timestamp"));
            credentialsDTO.setSignature(authMap.get("signature"));
//            credentialsDTO.setSecurityBody(getPostData(req));
            return credentialsDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
