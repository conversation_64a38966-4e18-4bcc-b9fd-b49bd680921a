<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.itheima.sfbx</groupId>
    <artifactId>sfbx-sms</artifactId>
    <version>2.0-SNAPSHOT</version>
  </parent>
  <artifactId>sms-web</artifactId>
  <name>sms-web</name>
  <!-- FIXME change it to the project's website -->
  <url>http://www.example.com</url>

  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-mybatis-plus</artifactId>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>file-interface</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-task-executor</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-seata</artifactId>
    </dependency>
    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-knife4j-web</artifactId>
    </dependency>

    <dependency>
      <groupId>com.itheima.sfbx</groupId>
      <artifactId>framework-rabbitmq</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>dysmsapi20170525</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tencentcloudapi</groupId>
      <artifactId>tencentcloud-sdk-java</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>logging-interceptor</artifactId>
          <groupId>com.squareup.okhttp</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baidubce</groupId>
      <artifactId>bce-java-sdk</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>jdk.tools</artifactId>
          <groupId>jdk.tools</groupId>
        </exclusion>
      </exclusions>
    </dependency>

  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.yml</include>
          <include>**/*.properties</include>
          <include>**/*.xml</include>
          <include>**/*.yaml</include>
          <include>**/*.txt</include>
        </includes>
      </resource>
    </resources>
    <finalName>sms-web</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
