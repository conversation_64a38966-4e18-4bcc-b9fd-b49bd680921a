<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>sfbx-cloud</artifactId>
        <groupId>com.itheima.sfbx</groupId>
        <version>2.0-SNAPSHOT</version>
    </parent>
    <!--权限处理模块-->
    <artifactId>sfbx-security</artifactId>
    <name>sfbx-security</name>
    <packaging>pom</packaging>
    <modules>
        <!--权限处理：接口模块-->
        <module>security-interface</module>
        <!--权限处理：认证模块-->
        <module>security-oauth</module>
        <!--权限处理：web模块-->
        <module>security-web</module>
    </modules>
    <!-- FIXME change it to the project's website -->
    <url>http://www.example.com</url>

</project>
