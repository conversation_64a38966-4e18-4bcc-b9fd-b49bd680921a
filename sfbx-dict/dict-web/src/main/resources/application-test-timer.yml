# 专门用于测试 Timer 修复的配置文件
spring:
  # 禁用 Nacos 配置中心，避免获取远程 Redis 配置
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
        
  # 禁用 Redis 相关配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration

  # 禁用缓存
  cache:
    type: none

# 数据库配置
spring:
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: root  # 请根据实际情况修改
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # MyBatis Plus 配置
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.itheima.sfbx: DEBUG
    org.springframework.cache: DEBUG
    org.redisson: OFF
    
# 服务器配置
server:
  port: 7071
