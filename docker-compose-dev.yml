version: '3.8'

services:
  redis:
    image: redis:latest
    container_name: sfbx-redis-dev
    ports:
      - "6379:6379"
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # 如果需要 MySQL，也可以添加
  # mysql:
  #   image: mysql:8.0
  #   container_name: sfbx-mysql-dev
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root
  #     MYSQL_DATABASE: sfbx_dict
  #   volumes:
  #     - mysql_data:/var/lib/mysql

volumes:
  redis_data:
  # mysql_data:
