package com.itheima.sfbx;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.itheima.sfbx.auth.BoleeEncryptor;
import com.itheima.sfbx.constants.BoleeSecurityConstant;
import com.itheima.sfbx.dto.CredentialsDTO;
import com.itheima.sfbx.dto.EncodeDTO;
import com.itheima.sfbx.utils.SecurityUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求模板类
 *
 * <p>提供统一的HTTP请求发送模板，集成了加密、解密、签名验证等安全功能。
 * 支持RSA+AES混合加密方式，确保数据传输安全。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>统一的HTTP请求发送接口</li>
 *   <li>自动加密请求数据（RSA+AES混合加密）</li>
 *   <li>自动解密响应数据</li>
 *   <li>数字签名验证</li>
 *   <li>可配置的请求超时设置</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>{@code
 * RequestTemplate template = RequestTemplate.builder()
 *     .privateKey("私钥")
 *     .publicKey("公钥")
 *     .appId("应用ID")
 *     .uriBuilder(new URIBuilder("http://example.com/api"))
 *     .build();
 *
 * ResponseDTO response = template.doRequest(requestData, ResponseDTO.class);
 * }</pre>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/12/28
 */
@Data
public class RequestTemplate {

    /**
     * RSA私钥，用于加密AES密钥和数字签名
     */
    private String privateKey;

    /**
     * RSA公钥，用于解密AES密钥和验证签名
     */
    private String publicKey;

    /**
     * 应用标识ID，用于身份认证
     */
    private String appId;

    /**
     * URI构建器，用于构建请求URL
     */
    private URIBuilder uriBuilder;

    /**
     * HTTP请求配置，包含超时设置等参数
     */
    private RequestConfig requestConfig;

    /**
     * 构造函数，使用Builder模式创建RequestTemplate实例
     *
     * @param privateKey RSA私钥
     * @param publicKey RSA公钥
     * @param appId 应用ID
     * @param uriBuilder URI构建器
     * @param requestConfig HTTP请求配置
     */
    @Builder
    public RequestTemplate(String privateKey, String publicKey, String appId, URIBuilder uriBuilder,RequestConfig requestConfig) {
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.appId = appId;
        this.uriBuilder = uriBuilder;
        this.requestConfig = requestConfig;
    }

    /**
     * 执行HTTP请求
     *
     * <p>该方法会自动处理以下流程：</p>
     * <ol>
     *   <li>创建带有认证信息的HTTP客户端</li>
     *   <li>使用RSA+AES混合加密请求数据</li>
     *   <li>发送HTTP POST请求</li>
     *   <li>验证响应签名</li>
     *   <li>解密响应数据</li>
     *   <li>将响应数据转换为指定类型</li>
     * </ol>
     *
     * @param <T> 响应数据类型
     * @param params 请求参数对象，会被转换为JSON格式
     * @param t 响应数据的目标类型
     * @return 解密后的响应数据对象
     * @throws URISyntaxException 当URI构建失败时抛出
     * @throws IOException 当HTTP请求失败时抛出
     * @throws RuntimeException 当参数解析为空时抛出
     */
    public <T> T doRequest(Object params,Class<T> t) throws URISyntaxException, IOException {
        // 创建带有认证信息的HTTP客户端
        CloseableHttpClient httpclient = BoleeHttpClientBuilder.create()
                .withCredentials(appId, privateKey)
                .withValidator(publicKey)
                .build();

        // 配置HTTP请求参数（超时设置等）
        RequestConfig requestConfig = null;
        if(ObjectUtil.isNull(requestConfig)) {
            // 使用默认配置：连接超时和读取超时都设置为100秒
            requestConfig = RequestConfig.custom()
                    .setSocketTimeout(100000)
                    .setConnectTimeout(100000)
                    .build();
        }else{
            // 使用自定义配置
            requestConfig = this.requestConfig;
        }
        try {
            // 构建请求URL
            URI url = uriBuilder.build();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);

            // 准备加密数据
            EncodeDTO encodeDTO = new EncodeDTO();
            JSONObject body = JSONUtil.parseObj(params);
            encodeDTO.setBody(body.toString());

            // 使用RSA+AES混合加密请求数据
            if(new BoleeEncryptor(privateKey).encode(encodeDTO)){
                // 设置加密后的请求体
                StringEntity stringEntity = new StringEntity(encodeDTO.getEncodeBody(), "utf-8");
                httpPost.setEntity(stringEntity);

                // 设置请求头：加密的AES密钥和内容类型
                httpPost.addHeader(BoleeSecurityConstant.HEAD_NAME_BODY_KEY,encodeDTO.getHeadAESSecurityKey());
                httpPost.addHeader("Content-Type","application/json;charset=utf-8");

                // 执行HTTP请求
                CloseableHttpResponse apiRes = httpclient.execute(httpPost);

                // 获取响应数据并解密
                HttpEntity entity = apiRes.getEntity();
                String content = EntityUtils.toString(entity);
                String responseJson = decryptRequestBody(content, apiRes.getFirstHeader(BoleeSecurityConstant.HEAD_NAME_BODY_KEY).getValue());

                // 检查解密结果
                if(StrUtil.isEmpty(responseJson)){
                    throw new RuntimeException("参数解析为空");
                }

                // 将JSON响应转换为目标对象类型
                return JSONUtil.toBean(responseJson, t);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return null;
    }

    /**
     * 解密响应数据
     *
     * <p>使用RSA+AES混合解密方式：</p>
     * <ol>
     *   <li>使用RSA公钥解密AES密钥</li>
     *   <li>使用解密后的AES密钥解密响应数据</li>
     * </ol>
     *
     * @param body 加密的响应数据
     * @param aesKeyRSA 使用RSA加密的AES密钥（从响应头获取）
     * @return 解密后的响应数据，解密失败时返回null
     */
    private String decryptRequestBody(String body, String aesKeyRSA) {
        try {
            // 第一步：使用RSA公钥解密AES密钥
            RSA rsa = SecureUtil.rsa(null, publicKey);
            String aesKey = SecurityUtil.decryptFromStringRSAPublicKey(rsa, aesKeyRSA);

            // 第二步：使用解密后的AES密钥解密响应数据
            // 使用CBC模式和ZeroPadding填充方式
            String requestBody = SecurityUtil.decryptFromStringAES(body, Mode.CBC, Padding.ZeroPadding,aesKey);
            return requestBody;
        } catch (Exception e) {
            e.printStackTrace();
            // 解密失败，返回null，调用方需要处理这种情况
            return null;
        }
    }

    /**
     * 构建请求认证参数
     *
     * <p>解析Authorization头部信息，提取认证相关参数。</p>
     * <p>Authorization头部格式示例：</p>
     * <pre>
     * timestamp=1694953272,nonce_str=xHct36JgHbj6tXBx,signature=xxx
     * </pre>
     *
     * @param authorization Authorization头部字符串
     * @param response HTTP响应对象（当前未使用）
     * @return 包含认证信息的CredentialsDTO对象，解析失败时返回null
     *
     * @deprecated 该方法当前未被使用，可能在未来版本中移除
     */
    private CredentialsDTO buildRequestParam(String authorization, CloseableHttpResponse response) {
        try {
            // 移除引号字符
            authorization = authorization.replaceAll("\"", "");
            // 按逗号分割参数
            String[] authorizations = authorization.split(",");
            Map<String, String> authMap = new HashMap<>();

            // 解析每个参数键值对
            for (String authorizationIndex : authorizations) {
                String[] split = authorizationIndex.split("=");
                authMap.put(split[0], split[1].substring(0, split[1].length()));
            }

            // 构建认证DTO对象
            CredentialsDTO credentialsDTO = new CredentialsDTO();
            credentialsDTO.setNonce(authMap.get("nonce_str"));        // 随机字符串
            credentialsDTO.setTimestamp(authMap.get("timestamp"));    // 时间戳
            credentialsDTO.setSignature(authMap.get("signature"));    // 数字签名
            // TODO: 需要设置安全请求体数据
            // credentialsDTO.setSecurityBody(getPostData(req));

            return credentialsDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
