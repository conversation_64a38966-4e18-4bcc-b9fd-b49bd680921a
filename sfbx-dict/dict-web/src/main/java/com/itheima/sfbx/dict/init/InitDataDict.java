package com.itheima.sfbx.dict.init;

import com.itheima.sfbx.dict.service.IDataDictService;
import com.itheima.sfbx.framework.commons.utils.EmptyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

/**
 * @ClassName InitDataDict.java
 * @Description 热加载数字字典
 */
@Slf4j
//@Component
public class InitDataDict implements DisposableBean {

    @Autowired
    IDataDictService dataDictService;

    private Timer timer;

    @Async
    @PostConstruct
    public void initDataDict(){
        timer = new Timer("InitDataDict-Timer", true); // 设置为守护线程
        timer.schedule(new InitTask(),10*1000);
    }

    @Override
    public void destroy() throws Exception {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    class InitTask extends TimerTask{

        @Override
        public void run() {
            try {
                //所有ParentKey的set集合
                Set<String> parentKeyAll = dataDictService.findParentKeyAll();
                if (EmptyUtil.isNullOrEmpty(parentKeyAll)){
                    return;
                }
                //初始化父亲目录下所有有效状态的数据
                parentKeyAll.forEach(n->{
                    dataDictService.findDataDictVOByParentKey(n);
                });
                //所有dataKey的set集合
                Set<String> dataKeyAll = dataDictService.findDataKeyAll();
                if (EmptyUtil.isNullOrEmpty(dataKeyAll)){ // 修复bug：这里应该检查dataKeyAll而不是parentKeyAll
                    return;
                }
                //初始化datakey对应有效状态的数据
                dataKeyAll.forEach(n->{
                    dataDictService.findDataDictVOByDataKey(n);
                });
                log.info("数据字典初始化完成");
            } catch (Exception e) {
                log.error("初始化数据字典失败", e);
            }
        }
    }
}
