#服务配置
server:
  #端口
  port: 7076
  #服务编码
  tomcat:
    uri-encoding: UTF-8
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  config:
    activate:
      on-profile:
        - test
  #应用配置
  application:
    #应用名称
    name: task-listener
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDRESS:nacos-service.yjy-public-sfbx-java.svc.cluster.local:20015} # nacos注册中心
        group: SEATA_GROUP7
        service: ${spring.application.name}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:PKsf*bxQ4;yP3a+}
      config:
        server-addr: ${NACOS_ADDRESS:nacos-service.yjy-public-sfbx-java.svc.cluster.local:20015} # nacos注册中心
        group: SEATA_GROUP
        file-extension: yml
        shared-configs: # 共享配置
          - data-id: shared-stream-rabbit-basic.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-stream-rabbit-sink-sms.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-stream-rabbit-sink-log.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
          - data-id: shared-stream-rabbit-sink-file.yml #配置文件名-DataId
            group: SEATA_GROUP
            refresh: false
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:PKsf*bxQ4;yP3a+}
logging:
  config: classpath:logback.xml
